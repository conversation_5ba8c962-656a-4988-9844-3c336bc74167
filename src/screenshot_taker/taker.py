# ── /src/screenshot_taker/taker.py ──────────────────────────────────────────────
from __future__ import annotations

import itertools
import logging
from datetime import datetime
from pathlib import Path
from typing import Iterable, Sequence

import mss
from PIL import Image

from .config import ScreenshotConfig

__all__: list[str] = ["ScreenshotTaker", "capture_once"]

_LOG = logging.getLogger(__name__)
_LOG.setLevel(logging.INFO)


def _todays_folder(root: Path) -> Path:
    """
    Create and return the folder for today's date.
  
    Requirement: F-5
    Version: v01
    """
    # Ensure root is a Path object
    root = Path(root) if not isinstance(root, Path) else root
    today = datetime.now().strftime("%Y%b%d").upper()
    folder = root / today
    folder.mkdir(parents=True, exist_ok=True)
    return folder


def _timestamp() -> str:
    """
    Generate a timestamp string for filenames (HHMMSS-µs).
  
    Requirement: F-5
    Version: v01
    """
    return datetime.now().strftime("%H%M%S-%f")


def _valid_monitors(requested: Sequence[int], total: int) -> list[int]:
    """
    Filter requested monitor indices to valid range.
  
    Requirement: F-4
    Version: v01
    """
    return [idx for idx in requested if 1 <= idx <= total]


def capture_once(cfg: ScreenshotConfig, seq_gen: Iterable[int] | None = None) -> None:
    """
    Capture screenshots for each configured monitor once.

    Requirement: F-4, F-5, N-4
    Version: v01
    """
    with mss.mss() as sct:
        total = len(sct.monitors) - 1  # index 0 = "all"
        targets = _valid_monitors(cfg.screens_to_capture, total)
        if not targets:
            _LOG.warning(
                "No valid screens to capture (requested=%s, total=%s)",
                cfg.screens_to_capture,
                total,
            )
            return

        folder = _todays_folder(cfg.output_root)
        seq_iter = seq_gen or itertools.count(1)

        for mon_idx in targets:
            try:
                src = sct.monitors[mon_idx]
                raw = sct.grab(src)  # raw BGRA bytes
                img = Image.frombytes("RGB", raw.size, raw.rgb)  # drop alpha

                filename = f"{_timestamp()}-{next(seq_iter):04d}.png"
                path = folder / filename
                img.save(path, "PNG")

                _LOG.debug("Saved screenshot: %s (monitor %s)", path, mon_idx)
            except Exception as exc:
                _LOG.warning(
                    "Capture failed for monitor %s: %s",
                    mon_idx,
                    exc,
                    exc_info=True,
                )


def _demo_main() -> None:
    """
    Simple demo function to test screenshot capture.

    This is a guard main that demonstrates the core screenshot functionality.
    """
    import logging

    # Setup basic logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    )

    print("=== Screenshot Taker Demo ===")
    print("Testing core screenshot functionality...")

    # Create a simple config for testing
    config = ScreenshotConfig(
        screens_to_capture=[1],  # Primary monitor only
        output_root=Path("./test_screenshots")
    )

    print("Configuration:")
    print(f"  - Screens to capture: {config.screens_to_capture}")
    print(f"  - Output directory: {config.output_root}")

    # Take a single screenshot
    print("\nTaking screenshot...")
    try:
        capture_once(config)

        # Check if screenshot was saved
        today_folder = _todays_folder(config.output_root)
        screenshots = list(today_folder.glob("*.png"))

        if screenshots:
            latest = max(screenshots, key=lambda p: p.stat().st_mtime)
            print("✅ Screenshot saved successfully!")
            print(f"   File: {latest}")
            print(f"   Size: {latest.stat().st_size} bytes")
        else:
            print("❌ No screenshot file found")

    except Exception as e:
        print(f"❌ Error taking screenshot: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    _demo_main()