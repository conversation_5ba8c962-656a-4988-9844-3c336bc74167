import os
import time
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import mss
from PIL import Image
from loguru import logger as log

from .config import cfg


class ScreenshotTaker:
    """Handles screenshot capture and storage."""
    
    def __init__(self):
        self.sct = mss.mss()
        self._ensure_output_dir()
        
    def _ensure_output_dir(self) -> None:
        """Create output directory structure if it doesn't exist."""
        today = datetime.now().strftime("%Y%m%d")
        self.daily_dir = Path(cfg.OUTPUT_DIR) / "screenshots" / today
        self.daily_dir.mkdir(parents=True, exist_ok=True)
        log.debug(f"Screenshot directory: {self.daily_dir}")
    
    def _get_monitors_to_capture(self) -> List[dict]:
        """Get list of monitors to capture based on configuration."""
        monitors = self.sct.monitors[1:]  # Skip the "All in One" monitor
        
        if cfg.SCREENS_TO_CAPTURE is None:
            return monitors
        
        selected_monitors = []
        for screen_idx in cfg.SCREENS_TO_CAPTURE:
            if 0 <= screen_idx < len(monitors):
                selected_monitors.append(monitors[screen_idx])
            else:
                log.warning(f"Screen index {screen_idx} out of range. Available: 0-{len(monitors)-1}")
        
        return selected_monitors or monitors  # Fallback to all if none valid
    
    def capture_screenshot(self) -> Optional[str]:
        """Capture screenshot and return the file path."""
        try:
            monitors = self._get_monitors_to_capture()
            timestamp = datetime.now().strftime("%H%M%S")
            
            if len(monitors) == 1:
                # Single monitor
                screenshot = self.sct.grab(monitors[0])
                img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                filename = f"{timestamp}-screen.png"
            else:
                # Multiple monitors - combine horizontally
                screenshots = []
                total_width = 0
                max_height = 0
                
                for monitor in monitors:
                    screenshot = self.sct.grab(monitor)
                    img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")
                    screenshots.append(img)
                    total_width += img.width
                    max_height = max(max_height, img.height)
                
                # Combine images
                combined = Image.new("RGB", (total_width, max_height))
                x_offset = 0
                for img in screenshots:
                    combined.paste(img, (x_offset, 0))
                    x_offset += img.width
                
                img = combined
                filename = f"{timestamp}-combined.png"
            
            filepath = self.daily_dir / filename
            img.save(filepath, "PNG")
            log.debug(f"Screenshot saved: {filepath}")
            return str(filepath)
            
        except Exception as e:
            log.error(f"Failed to capture screenshot: {e}")
            return None
    
    def start_continuous_capture(self) -> None:
        """Start continuous screenshot capture in a loop."""
        log.info(f"Starting continuous screenshot capture every {cfg.SCREENSHOT_INTERVAL}s")
        
        while True:
            try:
                self.capture_screenshot()
                time.sleep(cfg.SCREENSHOT_INTERVAL)
            except KeyboardInterrupt:
                log.info("Screenshot capture stopped by user")
                break
            except Exception as e:
                log.error(f"Error in continuous capture: {e}")
                time.sleep(cfg.SCREENSHOT_INTERVAL)  # Continue despite errors