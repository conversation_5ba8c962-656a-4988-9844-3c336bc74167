import asyncio
import threading
import time
from datetime import datetime
from typing import Optional

from loguru import logger as log
from rich.console import Console
from rich.live import Live
from rich.table import Table
from rich.panel import Panel

from .config import cfg
from .task_manager import TaskManager, Task
from .screen_capture import ScreenshotTaker
from .escalator import Escalator
from .agents import ReasoningAgent


class ProductivityGuard:
    """Main application orchestrating all components."""
    
    def __init__(self):
        self.console = Console()
        self.task_manager = TaskManager()
        self.screenshot_taker = ScreenshotTaker()
        self.escalator = Escalator()
        self.reasoning_agent = ReasoningAgent()
        
        self.running = False
        self.screenshot_thread: Optional[threading.Thread] = None
        self.analysis_thread: Optional[threading.Thread] = None
        
        self.stats = {
            "screenshots_taken": 0,
            "analyses_performed": 0,
            "justifications_requested": 0,
            "escalations_triggered": 0,
            "start_time": None
        }
    
    def start(self) -> None:
        """Start the productivity guard."""
        if self.running:
            log.warning("Productivity Guard is already running")
            return
        
        self.running = True
        self.stats["start_time"] = datetime.now()
        
        # Display startup info
        self._display_startup_info()
        
        # Start background threads
        self._start_screenshot_thread()
        self._start_analysis_thread()
        
        log.info("Productivity Guard started successfully")
        
        try:
            # Keep main thread alive and handle user interaction
            self._run_main_loop()
        except KeyboardInterrupt:
            log.info("Shutdown requested by user")
        finally:
            self.stop()
    
    def stop(self) -> None:
        """Stop the productivity guard."""
        if not self.running:
            return
        
        log.info("Stopping Productivity Guard...")
        self.running = False
        
        # Wait for threads to finish
        if self.screenshot_thread and self.screenshot_thread.is_alive():
            self.screenshot_thread.join(timeout=5)
        
        if self.analysis_thread and self.analysis_thread.is_alive():
            self.analysis_thread.join(timeout=5)
        
        self._display_shutdown_summary()
        log.info("Productivity Guard stopped")
    
    def _display_startup_info(self) -> None:
        """Display startup information."""
        current_task = self.task_manager.get_current_task()
        
        startup_info = f"""🎯 **Current Priority Task:** {current_task.title if current_task else 'No tasks loaded'}
📝 **Description:** {current_task.description if current_task else 'N/A'}

⚙️  **Configuration:**
   • Screenshot interval: {cfg.SCREENSHOT_INTERVAL}s
   • AI analysis interval: {cfg.MAIN_AI_INTERVAL_NORMAL}s (normal) / {cfg.MAIN_AI_INTERVAL_ESCALATION}s (escalation)
   • Justification timeout: {cfg.JUSTIFICATION_TIMEOUT}s
   • Output directory: {cfg.OUTPUT_DIR}

🚀 **Status:** Starting monitoring..."""
        
        panel = Panel(
            startup_info,
            title="[bold green]Productivity Guard - Starting[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    def _start_screenshot_thread(self) -> None:
        """Start screenshot capture thread."""
        def screenshot_loop():
            log.info("Screenshot thread started")
            while self.running:
                try:
                    screenshot_path = self.screenshot_taker.capture_screenshot()
                    if screenshot_path:
                        self.stats["screenshots_taken"] += 1
                        log.debug(f"Screenshot captured: {screenshot_path}")
                    
                    time.sleep(cfg.SCREENSHOT_INTERVAL)
                except Exception as e:
                    log.error(f"Error in screenshot thread: {e}")
                    time.sleep(cfg.SCREENSHOT_INTERVAL)
            
            log.info("Screenshot thread stopped")
        
        self.screenshot_thread = threading.Thread(target=screenshot_loop, daemon=True)
        self.screenshot_thread.start()
    
    def _start_analysis_thread(self) -> None:
        """Start AI analysis thread."""
        def analysis_loop():
            log.info("Analysis thread started")
            last_screenshot_path = None
            
            while self.running:
                try:
                    # Get current interval based on escalation state
                    interval = self.escalator.get_escalation_interval()
                    
                    # Get latest screenshot
                    screenshot_path = self._get_latest_screenshot()
                    current_task = self.task_manager.get_current_task()
                    
                    if screenshot_path and screenshot_path != last_screenshot_path and current_task:
                        # Analyze screenshot
                        analysis_result = self.reasoning_agent.analyze_screenshot(
                            screenshot_path, current_task
                        )
                        
                        self.stats["analyses_performed"] += 1
                        last_screenshot_path = screenshot_path
                        
                        log.debug(f"Analysis result: on_task={analysis_result['on_task']}, "
                                f"confidence={analysis_result['confidence']:.2f}")
                        
                        # Handle analysis result
                        self._handle_analysis_result(analysis_result, current_task)
                    
                    # Handle escalation notifications
                    if self.escalator.should_send_notification() and current_task:
                        self.escalator.send_escalation_notification(current_task)
                    
                    # Handle browser tab closing
                    if self.escalator.should_close_browser_tab():
                        self.escalator.close_browser_tab()
                    
                    time.sleep(interval)
                    
                except Exception as e:
                    log.error(f"Error in analysis thread: {e}")
                    time.sleep(cfg.MAIN_AI_INTERVAL_NORMAL)
            
            log.info("Analysis thread stopped")
        
        self.analysis_thread = threading.Thread(target=analysis_loop, daemon=True)
        self.analysis_thread.start()
    
    def _get_latest_screenshot(self) -> Optional[str]:
        """Get path to the most recent screenshot."""
        try:
            from pathlib import Path
            today = datetime.now().strftime("%Y%m%d")
            screenshot_dir = Path(cfg.OUTPUT_DIR) / "screenshots" / today
            
            if not screenshot_dir.exists():
                return None
            
            # Get most recent screenshot
            screenshots = list(screenshot_dir.glob("*.png"))
            if not screenshots:
                return None
            
            latest = max(screenshots, key=lambda p: p.stat().st_mtime)
            return str(latest)
            
        except Exception as e:
            log.error(f"Error getting latest screenshot: {e}")
            return None
    
    def _handle_analysis_result(self, analysis_result: dict, current_task: Task) -> None:
        """Handle the result of AI analysis."""
        if not analysis_result.get("on_task", True):
            confidence = analysis_result.get("confidence", 0.0)
            
            # Only prompt if confidence is high enough
            if confidence >= 0.7:
                log.warning(f"Off-task behavior detected (confidence: {confidence:.1%})")
                
                justification = self.escalator.prompt_for_justification(
                    current_task, analysis_result
                )
                
                self.stats["justifications_requested"] += 1
                
                if justification is None:
                    self.stats["escalations_triggered"] += 1
            else:
                log.debug(f"Low confidence off-task detection ignored (confidence: {confidence:.1%})")
    
    def _run_main_loop(self) -> None:
        """Run main application loop with status display."""
        with Live(self._create_status_table(), refresh_per_second=1) as live:
            while self.running:
                try:
                    live.update(self._create_status_table())
                    time.sleep(1)
                except KeyboardInterrupt:
                    break
    
    def _create_status_table(self) -> Table:
        """Create status display table."""
        table = Table(title="Productivity Guard Status", show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        current_task = self.task_manager.get_current_task()
        escalation_status = self.escalator.get_status_summary()
        
        # Runtime
        if self.stats["start_time"]:
            runtime = datetime.now() - self.stats["start_time"]
            runtime_str = str(runtime).split('.')[0]  # Remove microseconds
        else:
            runtime_str = "N/A"
        
        table.add_row("Runtime", runtime_str)
        table.add_row("Current Task", current_task.title if current_task else "None")
        table.add_row("Escalation Mode", "🚨 ACTIVE" if escalation_status["is_escalated"] else "✅ Normal")
        table.add_row("Screenshots Taken", str(self.stats["screenshots_taken"]))
        table.add_row("AI Analyses", str(self.stats["analyses_performed"]))
        table.add_row("Justifications Requested", str(self.stats["justifications_requested"]))
        table.add_row("Escalations Triggered", str(self.stats["escalations_triggered"]))
        table.add_row("Unanswered Prompts", str(escalation_status["unanswered_prompts"]))
        
        return table
    
    def _display_shutdown_summary(self) -> None:
        """Display shutdown summary."""
        if self.stats["start_time"]:
            runtime = datetime.now() - self.stats["start_time"]
            runtime_str = str(runtime).split('.')[0]
        else:
            runtime_str = "N/A"
        
        summary = f"""📊 **Session Summary:**
   • Runtime: {runtime_str}
   • Screenshots taken: {self.stats['screenshots_taken']}
   • AI analyses performed: {self.stats['analyses_performed']}
   • Justifications requested: {self.stats['justifications_requested']}
   • Escalations triggered: {self.stats['escalations_triggered']}

💾 **Data saved to:** {cfg.OUTPUT_DIR}

👋 **Thank you for using Productivity Guard!**"""
        
        panel = Panel(
            summary,
            title="[bold blue]Productivity Guard - Session Complete[/bold blue]",
            border_style="blue",
            padding=(1, 2)
        )
        
        self.console.print(panel)
    
    def load_tasks_from_text(self, task_text: str) -> None:
        """Load tasks from text input."""
        self.task_manager.load_tasks_from_text(task_text)
        
        current_task = self.task_manager.get_current_task()
        if current_task:
            self.console.print(f"[green]✅ Tasks loaded. Current priority: {current_task.title}[/green]")
    
    def add_task(self, title: str, description: str, priority: int) -> None:
        """Add a new task."""
        task = self.task_manager.add_task(title, description, priority)
        self.console.print(f"[green]✅ Task added: {task.title} (Priority: {priority})[/green]")
    
    def complete_current_task(self) -> bool:
        """Mark current task as completed."""
        current_task = self.task_manager.get_current_task()
        if current_task:
            success = self.task_manager.complete_task(current_task.id)
            if success:
                self.console.print(f"[green]✅ Task completed: {current_task.title}[/green]")
                
                # Reset escalation when task is completed
                self.escalator._reset_escalation()
                
                next_task = self.task_manager.get_current_task()
                if next_task:
                    self.console.print(f"[blue]🎯 Next priority task: {next_task.title}[/blue]")
            return success
        return False